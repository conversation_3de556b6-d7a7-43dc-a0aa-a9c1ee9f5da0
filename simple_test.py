#!/usr/bin/env python3
"""
Simple test script to verify the ConPort MCP Server datetime fixes.
This directly tests the handlers and models to ensure they work correctly.
"""

import sys
import os
from datetime import datetime, timedelta

# Add the source directory to Python path
sys.path.insert(0, os.path.join(os.getcwd(), 'src'))

try:
    from context_portal_mcp.db import models
    from context_portal_mcp.handlers import mcp_handlers
    print("✅ Successfully imported ConPort modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)

def test_datetime_models():
    """Test that the datetime models accept string timestamps."""
    print("\n🔧 Testing datetime model validation...")
    
    try:
        # Test GetItemHistoryArgs with string timestamps
        now = datetime.now()
        before_time = now.isoformat() + "Z"
        after_time = (now - timedelta(days=30)).isoformat() + "Z"
        
        args = models.GetItemHistoryArgs(
            workspace_id="test_workspace",
            item_type="product_context",
            limit=10,
            before_timestamp=before_time,
            after_timestamp=after_time,
            version=None
        )
        
        print(f"✅ GetItemHistoryArgs created successfully")
        print(f"   before_timestamp: {args.before_timestamp} (type: {type(args.before_timestamp).__name__})")
        print(f"   after_timestamp: {args.after_timestamp} (type: {type(args.after_timestamp).__name__})")
        
        # Test GetRecentActivitySummaryArgs with string timestamp
        recent_args = models.GetRecentActivitySummaryArgs(
            workspace_id="test_workspace",
            hours_ago=None,
            since_timestamp=after_time,
            limit_per_type=5
        )
        
        print(f"✅ GetRecentActivitySummaryArgs created successfully")
        print(f"   since_timestamp: {recent_args.since_timestamp} (type: {type(recent_args.since_timestamp).__name__})")
        
        return True
        
    except Exception as e:
        print(f"❌ Model validation failed: {e}")
        return False

def test_invalid_timestamp_validation():
    """Test that invalid timestamps are properly rejected."""
    print("\n⚠️  Testing invalid timestamp validation...")
    
    try:
        # This should fail with a validation error when the handler tries to parse it
        args = models.GetItemHistoryArgs(
            workspace_id="test_workspace",
            item_type="product_context",
            before_timestamp="invalid-timestamp"
        )
        
        print(f"✅ Model accepts invalid timestamp string (will be validated in handler)")
        print(f"   Invalid timestamp: {args.before_timestamp}")
        
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error with invalid timestamp: {e}")
        return False

def test_datetime_parsing():
    """Test the datetime parsing logic in handlers."""
    print("\n🕒 Testing datetime parsing logic...")
    
    try:
        from datetime import datetime
        
        # Test valid ISO 8601 formats
        test_timestamps = [
            "2023-12-01T10:30:00Z",
            "2023-12-01T10:30:00+00:00",
            "2023-12-01T10:30:00.123Z",
            datetime.now().isoformat() + "Z"
        ]
        
        for ts in test_timestamps:
            try:
                # This is the same parsing logic used in the handlers
                parsed = datetime.fromisoformat(ts.replace('Z', '+00:00'))
                print(f"✅ Parsed '{ts}' -> {parsed}")
            except ValueError as e:
                print(f"❌ Failed to parse '{ts}': {e}")
                return False
        
        # Test invalid format
        try:
            invalid_ts = "invalid-timestamp"
            parsed = datetime.fromisoformat(invalid_ts.replace('Z', '+00:00'))
            print(f"❌ Should have failed to parse '{invalid_ts}' but got: {parsed}")
            return False
        except ValueError:
            print(f"✅ Correctly rejected invalid timestamp: '{invalid_ts}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Datetime parsing test failed: {e}")
        return False

def test_model_json_schema():
    """Test that the models generate valid JSON schemas without datetime format issues."""
    print("\n📋 Testing JSON schema generation...")
    
    try:
        # Test GetItemHistoryArgs schema
        schema = models.GetItemHistoryArgs.model_json_schema()
        
        # Check that timestamp fields are strings, not datetime with format
        before_ts_prop = schema.get("properties", {}).get("before_timestamp", {})
        after_ts_prop = schema.get("properties", {}).get("after_timestamp", {})
        
        print(f"✅ GetItemHistoryArgs schema generated successfully")
        
        # Check the anyOf structure for optional fields
        if "anyOf" in before_ts_prop:
            for option in before_ts_prop["anyOf"]:
                if option.get("type") == "string":
                    if "format" in option and option["format"] == "date-time":
                        print(f"❌ Found problematic date-time format in before_timestamp")
                        return False
                    else:
                        print(f"✅ before_timestamp uses string type without date-time format")
        
        if "anyOf" in after_ts_prop:
            for option in after_ts_prop["anyOf"]:
                if option.get("type") == "string":
                    if "format" in option and option["format"] == "date-time":
                        print(f"❌ Found problematic date-time format in after_timestamp")
                        return False
                    else:
                        print(f"✅ after_timestamp uses string type without date-time format")
        
        # Test GetRecentActivitySummaryArgs schema
        recent_schema = models.GetRecentActivitySummaryArgs.model_json_schema()
        since_ts_prop = recent_schema.get("properties", {}).get("since_timestamp", {})
        
        print(f"✅ GetRecentActivitySummaryArgs schema generated successfully")
        
        if "anyOf" in since_ts_prop:
            for option in since_ts_prop["anyOf"]:
                if option.get("type") == "string":
                    if "format" in option and option["format"] == "date-time":
                        print(f"❌ Found problematic date-time format in since_timestamp")
                        return False
                    else:
                        print(f"✅ since_timestamp uses string type without date-time format")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON schema test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting ConPort MCP Server Datetime Fix Tests")
    
    tests = [
        ("Model Validation", test_datetime_models),
        ("Invalid Timestamp Handling", test_invalid_timestamp_validation),
        ("Datetime Parsing Logic", test_datetime_parsing),
        ("JSON Schema Generation", test_model_json_schema)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append(result)
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{test_name}: {status}")
        except Exception as e:
            print(f"\n❌ {test_name}: FAILED with exception: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n{'='*50}")
    print(f"📊 TEST SUMMARY")
    print('='*50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The datetime fixes are working correctly.")
        print("✅ The MCP server should now start without schema validation errors.")
    else:
        print("⚠️  Some tests failed. The datetime fixes may need additional work.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
