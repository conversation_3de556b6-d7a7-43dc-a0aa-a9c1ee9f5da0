# Security Policy

## Reporting a Vulnerability

We take the security of this project seriously. If you discover a vulnerability, please report it to us immediately.

**Please do not report security vulnerabilities through public GitHub issues.**

Instead, please email us directly at [YOUR_EMAIL_ADDRESS].

When reporting a vulnerability, please include:

*   A clear description of the vulnerability.
*   Steps to reproduce the vulnerability.
*   The version of the project affected.
*   Any potential impact of the vulnerability.

We will acknowledge your email within [X] business days and provide a more detailed response within [Y] business days, indicating the next steps in handling your report.

## Supported Versions

We currently support the following versions with security updates:

| Version | Supported          |
| ------- | ------------------ |
| [Latest] | :white_check_mark: |
| [Previous] | :x:                |

Please ensure you are using a supported version to receive security patches.

## Security Best Practices

[Optional: Add project-specific security best practices here, e.g., recommendations for API key management, dependency updates, etc.]