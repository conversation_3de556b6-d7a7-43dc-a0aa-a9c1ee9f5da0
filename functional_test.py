#!/usr/bin/env python3
"""
Functional test script for ConPort MCP Server.
This tests actual database operations and tool functionality.
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add the source directory to Python path
sys.path.insert(0, os.path.join(os.getcwd(), 'src'))

try:
    from context_portal_mcp.db import models, database
    from context_portal_mcp.handlers import mcp_handlers
    print("✅ Successfully imported ConPort modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)

def setup_test_workspace():
    """Set up a test workspace."""
    workspace_id = os.path.join(os.getcwd(), "test_workspace")
    Path(workspace_id).mkdir(exist_ok=True)
    return workspace_id

def test_log_and_get_decision(workspace_id):
    """Test logging and retrieving a decision."""
    print("\n📝 Testing decision logging and retrieval...")

    try:
        # Create a decision
        log_args = models.LogDecisionArgs(
            workspace_id=workspace_id,
            summary="Test decision for functional testing",
            rationale="Testing the MCP server functionality after datetime fixes",
            implementation_details="Created via functional test script",
            tags=["test", "functional", "mcp"]
        )

        # Log the decision
        result = mcp_handlers.handle_log_decision(log_args)
        print(f"✅ Decision logged: {result.get('summary', 'Unknown')}")
        decision_id = result.get('id')

        # Retrieve decisions
        get_args = models.GetDecisionsArgs(
            workspace_id=workspace_id,
            limit=5
        )

        decisions = mcp_handlers.handle_get_decisions(get_args)
        print(f"✅ Retrieved {len(decisions)} decisions")

        if decisions and decision_id:
            found_decision = next((d for d in decisions if d.get('id') == decision_id), None)
            if found_decision:
                print(f"✅ Found our test decision: {found_decision.get('summary', 'Unknown')}")
            else:
                print("⚠️  Test decision not found in retrieved list")

        return True

    except Exception as e:
        print(f"❌ Decision test failed: {e}")
        return False

def test_log_and_get_progress(workspace_id):
    """Test logging and retrieving progress entries."""
    print("\n📋 Testing progress logging and retrieval...")

    try:
        # Create a progress entry
        log_args = models.LogProgressArgs(
            workspace_id=workspace_id,
            status="IN_PROGRESS",
            description="Testing progress entry functionality",
            parent_id=None
        )

        # Log the progress
        result = mcp_handlers.handle_log_progress(log_args)
        print(f"✅ Progress logged: {result.get('description', 'Unknown')}")
        progress_id = result.get('id')

        # Retrieve progress entries
        get_args = models.GetProgressArgs(
            workspace_id=workspace_id,
            limit=5
        )

        progress_entries = mcp_handlers.handle_get_progress(get_args)
        print(f"✅ Retrieved {len(progress_entries)} progress entries")

        if progress_entries and progress_id:
            found_entry = next((p for p in progress_entries if p.get('id') == progress_id), None)
            if found_entry:
                print(f"✅ Found our test progress: {found_entry.get('description', 'Unknown')}")
            else:
                print("⚠️  Test progress entry not found in retrieved list")

        return True

    except Exception as e:
        print(f"❌ Progress test failed: {e}")
        return False

def test_item_history_with_timestamps(workspace_id):
    """Test the fixed get_item_history functionality with string timestamps."""
    print("\n🕒 Testing item history with timestamp filtering...")

    try:
        # First, let's update the product context to create some history
        update_args = models.UpdateProductContextArgs(
            workspace_id=workspace_id,
            content={
                "project_name": "ConPort MCP Test",
                "description": "Testing the MCP server functionality",
                "version": "1.0.0"
            }
        )

        # Update product context
        mcp_handlers.handle_update_product_context(update_args)
        print("✅ Updated product context to create history")

        # Now test getting history with timestamp filters
        now = datetime.now()
        before_time = now.isoformat() + "Z"
        after_time = (now - timedelta(hours=1)).isoformat() + "Z"

        history_args = models.GetItemHistoryArgs(
            workspace_id=workspace_id,
            item_type="product_context",
            limit=10,
            before_timestamp=before_time,
            after_timestamp=after_time
        )

        # Get history
        history = mcp_handlers.handle_get_item_history(history_args)
        print(f"✅ Retrieved {len(history)} history entries")
        print(f"   Time range: {after_time} to {before_time}")

        if history:
            latest = history[0]
            print(f"   Latest entry: version {latest.get('version', 'unknown')}")

        return True

    except Exception as e:
        print(f"❌ Item history test failed: {e}")
        return False

def test_invalid_timestamp_handling(workspace_id):
    """Test that invalid timestamps are properly handled."""
    print("\n⚠️  Testing invalid timestamp error handling...")

    try:
        # Try to get history with an invalid timestamp
        history_args = models.GetItemHistoryArgs(
            workspace_id=workspace_id,
            item_type="product_context",
            before_timestamp="this-is-not-a-valid-timestamp"
        )

        # This should raise a ContextPortalError (which wraps the ToolArgumentError)
        try:
            history = mcp_handlers.handle_get_item_history(history_args)
            print("❌ Expected error for invalid timestamp, but got result")
            return False
        except mcp_handlers.ContextPortalError as e:
            if "Invalid before_timestamp format" in str(e):
                print("✅ Invalid timestamp properly rejected with helpful error message")
                print(f"   Error: {e}")
                return True
            else:
                print(f"❌ Got ContextPortalError but wrong message: {e}")
                return False
        except mcp_handlers.ToolArgumentError as e:
            if "Invalid before_timestamp format" in str(e):
                print("✅ Invalid timestamp properly rejected with helpful error message")
                print(f"   Error: {e}")
                return True
            else:
                print(f"❌ Got ToolArgumentError but wrong message: {e}")
                return False

    except Exception as e:
        print(f"❌ Invalid timestamp test failed unexpectedly: {e}")
        return False

def test_recent_activity_with_timestamp(workspace_id):
    """Test the recent activity summary with timestamp filtering."""
    print("\n📊 Testing recent activity summary with timestamp...")

    try:
        # Test with a timestamp string
        since_time = (datetime.now() - timedelta(hours=2)).isoformat() + "Z"

        activity_args = models.GetRecentActivitySummaryArgs(
            workspace_id=workspace_id,
            hours_ago=None,
            since_timestamp=since_time,
            limit_per_type=5
        )

        # Get recent activity
        activity = mcp_handlers.handle_get_recent_activity_summary(activity_args)
        print(f"✅ Retrieved recent activity summary")
        print(f"   Since: {since_time}")
        print(f"   Recent decisions: {len(activity.get('recent_decisions', []))}")
        print(f"   Recent progress: {len(activity.get('recent_progress_entries', []))}")

        return True

    except Exception as e:
        print(f"❌ Recent activity test failed: {e}")
        return False

def main():
    """Run all functional tests."""
    print("🚀 Starting ConPort MCP Server Functional Tests")

    # Setup
    workspace_id = setup_test_workspace()
    print(f"📁 Test workspace: {workspace_id}")

    tests = [
        ("Decision Logging & Retrieval", lambda: test_log_and_get_decision(workspace_id)),
        ("Progress Logging & Retrieval", lambda: test_log_and_get_progress(workspace_id)),
        ("Item History with Timestamps", lambda: test_item_history_with_timestamps(workspace_id)),
        ("Invalid Timestamp Handling", lambda: test_invalid_timestamp_handling(workspace_id)),
        ("Recent Activity with Timestamp", lambda: test_recent_activity_with_timestamp(workspace_id))
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)

        try:
            result = test_func()
            results.append(result)
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{test_name}: {status}")
        except Exception as e:
            print(f"\n❌ {test_name}: FAILED with exception: {e}")
            results.append(False)

    # Summary
    passed = sum(results)
    total = len(results)

    print(f"\n{'='*60}")
    print(f"📊 FUNCTIONAL TEST SUMMARY")
    print('='*60)
    print(f"Tests passed: {passed}/{total}")

    if passed == total:
        print("🎉 All functional tests passed!")
        print("✅ The MCP server is working correctly with the datetime fixes.")
        print("✅ Database operations are functioning properly.")
        print("✅ Timestamp parsing and validation are working as expected.")
    else:
        print("⚠️  Some functional tests failed.")
        print("   The MCP server may have issues beyond the datetime fixes.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
