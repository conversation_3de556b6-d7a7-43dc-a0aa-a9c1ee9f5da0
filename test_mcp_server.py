#!/usr/bin/env python3
"""
Test script for the ConPort MCP Server.
This script tests various MCP tools to ensure they work correctly after the datetime fix.
"""

import asyncio
import json
import subprocess
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, List

class MCPTester:
    def __init__(self, workspace_id: str):
        self.workspace_id = workspace_id
        self.server_process = None
        
    async def start_server(self):
        """Start the MCP server process."""
        cmd = [
            sys.executable, "-m", "context_portal_mcp.main",
            "--mode", "stdio",
            "--workspace_id", self.workspace_id,
            "--log-file", "./logs/conport_test.log",
            "--log-level", "INFO"
        ]
        
        self.server_process = await asyncio.create_subprocess_exec(
            *cmd,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd="C:\\Users\\<USER>\\mcp-servers\\conport-venv"
        )
        
        print("✅ MCP Server started successfully")
        
    async def send_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Send a JSON-RPC request to the MCP server."""
        if not self.server_process:
            raise RuntimeError("Server not started")
            
        request_json = json.dumps(request) + "\n"
        self.server_process.stdin.write(request_json.encode())
        await self.server_process.stdin.drain()
        
        # Read response
        response_line = await self.server_process.stdout.readline()
        response = json.loads(response_line.decode())
        return response
        
    async def test_list_tools(self):
        """Test listing available tools."""
        print("\n🔧 Testing list_tools...")
        
        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/list"
        }
        
        response = await self.send_request(request)
        
        if "result" in response and "tools" in response["result"]:
            tools = response["result"]["tools"]
            print(f"✅ Found {len(tools)} tools:")
            for tool in tools[:5]:  # Show first 5 tools
                print(f"   - {tool['name']}: {tool.get('description', 'No description')[:60]}...")
            if len(tools) > 5:
                print(f"   ... and {len(tools) - 5} more tools")
            return True
        else:
            print(f"❌ Failed to list tools: {response}")
            return False
            
    async def test_log_decision(self):
        """Test logging a decision."""
        print("\n📝 Testing log_decision...")
        
        request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/call",
            "params": {
                "name": "log_decision",
                "arguments": {
                    "workspace_id": self.workspace_id,
                    "summary": "Test decision for MCP server validation",
                    "rationale": "Testing that the MCP server works correctly after datetime fixes",
                    "implementation_details": "Created a test decision to verify functionality",
                    "tags": ["test", "mcp", "validation"]
                }
            }
        }
        
        response = await self.send_request(request)
        
        if "result" in response:
            result = response["result"]
            if "content" in result and isinstance(result["content"], list):
                decision_data = result["content"][0] if result["content"] else {}
                print(f"✅ Decision logged successfully with ID: {decision_data.get('id', 'unknown')}")
                return decision_data.get('id')
            else:
                print(f"✅ Decision logged: {result}")
                return True
        else:
            print(f"❌ Failed to log decision: {response}")
            return False
            
    async def test_get_decisions(self):
        """Test getting decisions."""
        print("\n📋 Testing get_decisions...")
        
        request = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "get_decisions",
                "arguments": {
                    "workspace_id": self.workspace_id,
                    "limit": 5
                }
            }
        }
        
        response = await self.send_request(request)
        
        if "result" in response:
            result = response["result"]
            if "content" in result and isinstance(result["content"], list):
                decisions = result["content"]
                print(f"✅ Retrieved {len(decisions)} decisions")
                if decisions:
                    latest = decisions[0]
                    print(f"   Latest: {latest.get('summary', 'No summary')[:50]}...")
                return True
            else:
                print(f"✅ Got decisions: {result}")
                return True
        else:
            print(f"❌ Failed to get decisions: {response}")
            return False
            
    async def test_get_item_history_with_timestamps(self):
        """Test the fixed get_item_history tool with string timestamps."""
        print("\n🕒 Testing get_item_history with timestamp strings...")
        
        # Test with ISO 8601 timestamp strings
        now = datetime.now()
        before_time = now.isoformat() + "Z"
        after_time = (now - timedelta(days=30)).isoformat() + "Z"
        
        request = {
            "jsonrpc": "2.0",
            "id": 4,
            "method": "tools/call",
            "params": {
                "name": "get_item_history",
                "arguments": {
                    "workspace_id": self.workspace_id,
                    "item_type": "product_context",
                    "limit": 10,
                    "before_timestamp": before_time,
                    "after_timestamp": after_time
                }
            }
        }
        
        response = await self.send_request(request)
        
        if "result" in response:
            result = response["result"]
            if "content" in result and isinstance(result["content"], list):
                history = result["content"]
                print(f"✅ Retrieved {len(history)} history entries for product_context")
                print(f"   Timestamp range: {after_time} to {before_time}")
                return True
            else:
                print(f"✅ Got history: {result}")
                return True
        else:
            print(f"❌ Failed to get item history: {response}")
            return False
            
    async def test_invalid_timestamp_format(self):
        """Test that invalid timestamp formats are properly handled."""
        print("\n⚠️  Testing invalid timestamp format handling...")
        
        request = {
            "jsonrpc": "2.0",
            "id": 5,
            "method": "tools/call",
            "params": {
                "name": "get_item_history",
                "arguments": {
                    "workspace_id": self.workspace_id,
                    "item_type": "product_context",
                    "before_timestamp": "invalid-timestamp-format"
                }
            }
        }
        
        response = await self.send_request(request)
        
        if "error" in response:
            error_msg = response["error"].get("message", "")
            if "Invalid before_timestamp format" in error_msg:
                print("✅ Invalid timestamp format properly rejected with helpful error message")
                return True
            else:
                print(f"✅ Got error (as expected): {error_msg}")
                return True
        else:
            print(f"❌ Expected error for invalid timestamp, but got: {response}")
            return False
            
    async def cleanup(self):
        """Clean up the server process."""
        if self.server_process:
            self.server_process.terminate()
            await self.server_process.wait()
            print("\n🧹 Server process terminated")

async def main():
    """Run all tests."""
    workspace_id = "C:\\Users\\<USER>\\mcp-servers\\conport-venv"
    tester = MCPTester(workspace_id)
    
    try:
        print("🚀 Starting ConPort MCP Server Tests")
        print(f"📁 Workspace: {workspace_id}")
        
        await tester.start_server()
        
        # Give server a moment to fully start
        await asyncio.sleep(2)
        
        # Run tests
        tests = [
            tester.test_list_tools(),
            tester.test_log_decision(),
            tester.test_get_decisions(),
            tester.test_get_item_history_with_timestamps(),
            tester.test_invalid_timestamp_format()
        ]
        
        results = await asyncio.gather(*tests, return_exceptions=True)
        
        # Count successes
        successes = sum(1 for result in results if result is True)
        total = len(results)
        
        print(f"\n📊 Test Results: {successes}/{total} tests passed")
        
        if successes == total:
            print("🎉 All tests passed! The MCP server is working correctly.")
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
            
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
